{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"tank-monitor-frontend": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/tank-monitor-frontend", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "public"}, "src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all", "serviceWorker": "ngsw-config.json"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "tank-monitor-frontend:build:production"}, "development": {"buildTarget": "tank-monitor-frontend:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}, "src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.css"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}, "deploy": {"builder": "angular-cli-ghpages:deploy"}}}}, "cli": {"schematicCollections": ["angular-eslint"]}}