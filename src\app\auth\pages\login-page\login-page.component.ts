import { Component, inject, signal } from '@angular/core'
import { Form<PERSON><PERSON>er, ReactiveFormsModule, Validators } from '@angular/forms'
import { Router, RouterLink } from '@angular/router'
import { ErrorAlertComponent } from '@common/components/error-alert/error-alert.component'
import { emailPattern } from '@common/forms/patterns/email.pattern'
import { FormGroupUtils } from '@common/forms/utils/form-group.utils'
import { AuthService } from '@auth/services/auth.service'

@Component({
  selector: 'app-login-page',
  imports: [RouterLink, ReactiveFormsModule, ErrorAlertComponent],
  templateUrl: './login-page.component.html',
})
export class LoginPageComponent {
  private fb = inject(FormBuilder)

  private router = inject(Router)

  authService = inject(AuthService)

  error = signal('')

  isPosting = signal(false)

  form = this.fb.group({
    email: ['', [Validators.required, Validators.pattern(emailPattern)]],
    password: ['', [Validators.required, Validators.minLength(8)]],
  })

  formUtils = new FormGroupUtils(this.form)

  onSubmit() {
    this.form.markAllAsTouched()

    if (this.form.invalid) return

    const { email, password } = this.form.value

    this.isPosting.set(true)

    this.authService
      .login({
        email: email!,
        password: password!,
      })
      .subscribe(({ isAuthenticated }) => {
        if (isAuthenticated) {
          this.router.navigateByUrl('/')
          return
        }
        this.error.set('Credenciales incorrectas')
        this.isPosting.set(false)

        setTimeout(() => {
          this.error.set('')
        }, 2000)
      })
  }
}
