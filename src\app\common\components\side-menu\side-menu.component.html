<div class="drawer lg:drawer-open">
  <input id="my-drawer-2" type="checkbox" class="drawer-toggle" />
  <div class="drawer-content">
    <!-- Page content here -->
    <ng-content></ng-content>
  </div>
  <div class="drawer-side">
    <label
      for="my-drawer-2"
      aria-label="close sidebar"
      class="drawer-overlay"></label>
    <ul class="menu bg-base-200 text-base-content min-h-full w-80 p-4 gap-2">
      <p class="text-2xl font-bold mb-2">Menú</p>
      <!-- Sidebar content here -->
      <!-- TODO: Componetizar botones para hacerlo con @for -->
      <li>
        <label
          for="my-drawer-2"
          routerLink="/"
          class="btn btn-secondary w-full drawer-button">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M3 13h8V3H3zm0 8h8v-6H3zm10 0h8V11h-8zm0-18v6h8V3z" />
          </svg>
          <a>Dashboard</a>
        </label>
      </li>
      <li>
        <label
          for="my-drawer-2"
          routerLink="/sensor"
          class="btn btn-secondary w-full drawer-button">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M9 4H7v2H5v12h2v2h2v-2h2V6H9zm10 4h-2V4h-2v4h-2v7h2v5h2v-5h2z" />
          </svg>
          <a>Sensores</a>
        </label>
      </li>
      <li>
        <label
          for="my-drawer-2"
          routerLink="/trigger"
          class="btn btn-secondary w-full drawer-button">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="m22 12l-4 4l-1.41-1.41L18.17 13h-5.23A8.97 8.97 0 0 1 8 20.05A3.005 3.005 0 0 1 5 23c-1.66 0-3-1.34-3-3s1.34-3 3-3c.95 0 1.78.45 2.33 1.14A6.97 6.97 0 0 0 10.91 13h-3.1C7.4 14.16 6.3 15 5 15c-1.66 0-3-1.34-3-3s1.34-3 3-3c1.3 0 2.4.84 2.82 2h3.1c-.32-2.23-1.69-4.1-3.59-5.14C6.78 6.55 5.95 7 5 7C3.34 7 2 5.66 2 4s1.34-3 3-3a2.99 2.99 0 0 1 2.99 2.95A8.97 8.97 0 0 1 12.93 11h5.23l-1.58-1.59L18 8z" />
          </svg>
          <a>Triggers</a>
        </label>
      </li>
      <li>
        <label
          for="my-drawer-2"
          routerLink="/"
          class="btn btn-secondary w-full drawer-button">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M19 12h-2v3h-3v2h5zM7 9h3V7H5v5h2zm14-6H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m0 16.01H3V4.99h18z" />
          </svg>
          <a>Diagramas</a>
        </label>
      </li>
    </ul>
  </div>
</div>
