import { JsonPipe } from '@angular/common'
import {
  Component,
  computed,
  inject,
  linkedSignal,
  OnInit,
  signal,
} from '@angular/core'
import { AuthService } from '@auth/services/auth.service'
import { ActuatorTypePipe } from '../../pipes/actuator-type.pipe'
import { RouterLink } from '@angular/router'
import { CreateActuatorModalComponent } from '../../components/create-actuator-modal/create-actuator-modal.component'

@Component({
  selector: 'app-my-actuators-page',
  imports: [ActuatorTypePipe, CreateActuatorModalComponent],
  templateUrl: './my-actuators-page.component.html',
})
export default class MyActuatorsPageComponent {
  authService = inject(AuthService)


  // actuatorsStatusService = inject(ActuatorsStatusService)

  query = signal('')

  // actuatorsStatus = this.actuatorsStatusService.actuatorsStatus
  actuatorsStatus = signal([{ actuatorId: '5e7f0001-cb5d-41b5-afae-343d0fccaaba', connected: true }])

  connectedActuators = computed(() => {
    if (this.actuatorsStatus() === null) return []
    return this.actuatorsStatus()!
      .filter(s => s.connected)
      .map(s => s.actuatorId)
  })

  userHasActuators = computed(() => {
    const actuators = this.authService.user()?.actuators
    return actuators !== undefined && actuators.length !== 0
  })

  actuators = computed(() => {
    const query = this.query().toLowerCase().trim()

    const actuators = this.authService.user()?.actuators!

    if (!this.query()) return actuators

    return actuators.filter(s => s.name.toLowerCase().includes(query))
  })
}
