import { HttpClient } from '@angular/common/http'
import { inject, Injectable } from '@angular/core'
import { catchError, Observable, of } from 'rxjs'
import { environment } from '@environments/environment.development'
import { AuthService } from '@auth/services/auth.service'
import { PaginationDTO } from '@common/dto/pagination.dto'
import { Pagination } from '@common/pagination/pagination.interface'
import { Actuator } from '../interfaces/actuator'

@Injectable({ providedIn: 'root' })
export class ActuatorApiService {
  private http = inject(HttpClient)

  private authService = inject(AuthService)

  getAll(dto?: PaginationDTO): Observable<Pagination<Actuator> | null> {
    const pagination = dto?.limit ? `?limit=${dto.limit}&page=${dto.page}` : ''
    return this.http
      .get<Pagination<Actuator>>(
        `http://${environment.baseUrl}/actuator/many` + pagination,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error obteniendo actuadores: ${resp}`)
          return of(null)
        })
      )
  }
}
