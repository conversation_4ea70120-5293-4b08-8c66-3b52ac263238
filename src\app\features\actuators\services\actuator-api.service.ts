import { HttpClient } from '@angular/common/http'
import { inject, Injectable } from '@angular/core'
import { catchError, Observable, of } from 'rxjs'
import { environment } from '@environments/environment.development'
import { AuthService } from '@auth/services/auth.service'
import { PaginationDTO } from '@common/dto/pagination.dto'
import { Pagination } from '@common/pagination/pagination.interface'
import { Actuator } from '../interfaces/actuator'
import { CreateActuatorDTO } from './dto/create-actuator.dto'
import { CreateActuatorResponse } from './api-responses/create-actuator.response'
import { FindOneActuatorDTO } from './dto/find-one-actuator.dto'
import { FindOneActuatorResponse } from './api-responses/find-one-actuator.response'

@Injectable({ providedIn: 'root' })
export class ActuatorApiService {
  private http = inject(HttpClient)

  private authService = inject(AuthService)

  create(dto: CreateActuatorDTO): Observable<CreateActuatorResponse | null> {
    return this.http
      .post<CreateActuatorResponse>(
        `http://${environment.baseUrl}/actuator/add-actuator-user`,
        dto,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error creando actuator: ${resp}`)
          return of(null)
        })
      )
  }

  findOne(dto: FindOneActuatorDTO): Observable<FindOneActuatorResponse | null> {
    return this.http
      .get<FindOneActuatorResponse>(
        `http://${environment.baseUrl}/actuator/one/${dto.id}`,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe()
  }

  getAll(dto?: PaginationDTO): Observable<Pagination<Actuator> | null> {
    const pagination = dto?.limit ? `?limit=${dto.limit}&page=${dto.page}` : ''
    return this.http
      .get<Pagination<Actuator>>(
        `http://${environment.baseUrl}/actuator/many` + pagination,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error obteniendo actuadores: ${resp}`)
          return of(null)
        })
      )
  }
}
