import { Injectable, signal, inject } from '@angular/core'
import { ActuatorSocketService } from './actuator-socket.service'
import { AuthService } from '../../../../auth/services/auth.service'
import { HttpClient } from '@angular/common/http'

export type ActuatorStatus = {
  connected: boolean
  actuatorId: string
}

@Injectable({ providedIn: 'root' })
export class ActuatorsStatusService {
  actuatorsStatus = signal<ActuatorStatus[] | null>(null)

  actuatorService = inject(ActuatorSocketService)

  authService = inject(AuthService)
  private http = inject(HttpClient)
  constructor() {
    this.http
      .get('http://your_emqx_host:18083/api/v5/clients', {
        auth: {
          username: 'your_emqx_username',
          password: 'your_emqx_password',
        },
      })
      .subscribe(resp => {
        this.actuatorsStatus.set(resp)
      })

    this.actuatorsStatus.set([])
    this.actuatorService.onMessage('actuators-status', resp => {
      this.actuatorsStatus.update(value => {
        if (value === null) return [resp]

        return value
          .map(s => {
            if (s.actuatorId === resp.actuatorId) {
              return
            }
            return s
          })
          .concat([resp])
          .filter(s => s !== undefined)
      })
    })
    this.authService.user()!.actuators.forEach(actuator => {
      this.actuatorService.sendMessage('get-actuators-status', {
        actuatorId: actuator.id,
      })
    })
  }
}
