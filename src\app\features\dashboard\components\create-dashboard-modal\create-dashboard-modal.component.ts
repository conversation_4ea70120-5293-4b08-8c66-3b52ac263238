import {
  Component,
  computed,
  inject,
  input,
  output,
  Signal,
  signal,
} from '@angular/core'
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms'
import { AuthService } from '@auth/services/auth.service'
import { FormGroupUtils } from '@common/forms/utils/form-group.utils'
import { DashboardApiService } from '../../services/api/dashboard-api.service'
import { DashboardTypesEnum } from '../../interfaces/dashboard-types.enum'
import { DashboardTypePipe } from '../../pipes/dashboard-type.pipe'

@Component({
  selector: 'app-create-dashboard-modal',
  imports: [ReactiveFormsModule, DashboardTypePipe],
  templateUrl: './create-dashboard-modal.component.html',
})
export class CreateDashboardModalComponent {

  private fb = inject(FormBuilder)

  private authService = inject(AuthService)
  private dashbardApiService = inject(DashboardApiService)
  sensors = computed(() => this.authService.user()?.sensors)

  createForm = this.fb.group({
    sensorId: [null, Validators.required],
    type: [null, Validators.required],
  })

  createFormUtils = new FormGroupUtils(this.createForm)

  isCreating = signal(false)

  created = output<boolean>()

  dashboardTypes = computed(() => {
    return Object.values(DashboardTypesEnum)
  })

  onCancel() {
    setTimeout(() => {
      this.createForm.reset()
    }, 250)
  }

  onSubmitCreate() {
    this.createForm.markAllAsTouched()

    if (!this.createForm.valid) return

    this.isCreating.set(true)

    const { sensorId, type } = this.createForm.value

    this.dashbardApiService
      .create({
        sensorId: sensorId!,
        type: type! as DashboardTypesEnum,
      })
      .subscribe(response => {
        this.isCreating.set(false)
        if (!response) return
        this.created.emit(true)
        document.getElementById("cancel-button")?.click()
      })
  }
}
