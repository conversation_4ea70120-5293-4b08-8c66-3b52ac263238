<div class="flex gap-5 mb-10 flex-wrap justify-between">
  <div class="flex gap-5">
    <h2 class="text-3xl font-bold">Dashboard</h2>
  </div>
  <div class="w-fit flex justify-center">
    <select class="select w-fit" (change)="onFilterChange($event)">
      @for (option of defaultFilterOptionsList; track option) {
        <option [value]="option" [selected]="filterOption() === option">
          {{ option | deffilteroption }}
        </option>
      }
    </select>
  </div>
  <div class="flex w-fit justify-center gap-5">
    <button
      class="btn btn-primary rounded-md"
      onclick="create_dashboard_modal.showModal()">
      +
    </button>
  </div>
</div>
@if (
  DashboardResources.hasValue() &&
  SensorData().length == DashboardResources.value().length
) {
  <div
    class="grid grid-cols-1 md:grid-cols-2 sm:grid-cols-1 xs:grid-cols-1 gap-2">
    @for (item of DashboardResources.value(); track $index) {
      @if (item.type == dashboardTypes.MEASURE) {
        <dashboard-chart
          [sensor]="SensorData()[$index]"
          [measurements]="item.measurements.reverse()"
          [id]="item.id"
          (deleted)="onChangedDashboard($event)" />
      } @else {
        <single-measure-chart
          [sensor]="SensorData()[$index]"
          [measurements]="toSignal(item.measurements)"
          [type]="item.type"
          [id]="item.id"
          (deleted)="onChangedDashboard($event)" />
      }
    }
  </div>
}
<app-create-dashboard-modal (created)="onChangedDashboard($event)" />
