import { HttpClient } from '@angular/common/http'
import { inject, Injectable } from '@angular/core'
import { catchError, map, Observable, of } from 'rxjs'
import { environment } from '@environments/environment.development'
import { AuthService } from '@auth/services/auth.service'
import { FindManyDashboardsResponse } from './api-responses/find-many-dashboards.response'
import { FindManyDashboardsDTO } from './dto/find-many-dashboards.dto'
import { CreateDashboardDto } from './dto/create-dashboard.dto'
import { CreateDashboardResponse } from './api-responses/create-dashboard.response'

@Injectable({ providedIn: 'root' })
export class DashboardApiService {
  private http = inject(HttpClient)

  private authService = inject(AuthService)

  create(dto: CreateDashboardDto): Observable<CreateDashboardResponse | null> {
    return this.http
      .post<CreateDashboardResponse>(
        `http://${environment.baseUrl}/dashboard/create`,
        dto,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error creando dashboard: ${resp}`)
          return of(null)
        })
      )
  }

  delete(dashboardId: string): Observable<string | null> {
    return this.http
      .delete<string>(
        `http://${environment.baseUrl}/dashboard/delete/${dashboardId}`,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error eliminando dashboard: ${resp}`)
          return of(null)
        })
      )
  }

  findManyDashboards(
    dto: FindManyDashboardsDTO
  ): Observable<FindManyDashboardsResponse[]> {
    return this.http
      .get<FindManyDashboardsResponse[]>(
        `http://${environment.baseUrl}/dashboard/many`,
        {
          params: {
            limit: 10,
            page: 1,
            startDate: dto.startDate.toISOString(),
            endDate: dto.endDate.toISOString(),
          },
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(
            `Error obteniendo el dashboard: ${JSON.stringify(resp)}`
          )
          return of([])
        })
      )
  }
}
