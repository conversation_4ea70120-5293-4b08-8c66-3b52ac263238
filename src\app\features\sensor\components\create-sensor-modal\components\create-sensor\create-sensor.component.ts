import {
  Component,
  computed,
  inject,
  input,
  Signal,
  signal,
} from '@angular/core'
import { Form<PERSON>uilder, ReactiveFormsModule, Validators } from '@angular/forms'
import { SensorType } from '@auth/interfaces/user'
import { AuthService } from '@auth/services/auth.service'
import { FormGroupUtils } from '@common/forms/utils/form-group.utils'
import { SensorTypePipe } from 'src/app/features/sensor/pipes/sensor-type.pipe'
import { SensorApiService } from 'src/app/features/sensor/services/api/sensor-api.service'

@Component({
  selector: 'create-sensor',
  imports: [ReactiveFormsModule, SensorTypePipe],
  templateUrl: './create-sensor.component.html',
})
export class CreateSensorComponent {
  sensorToken = input.required<(value: string) => void>()
  isSyncing = input.required<(value: boolean) => void>()

  private fb = inject(FormBuilder)

  private authService = inject(AuthService)
  private sensorApiService = inject(SensorApiService)

  createForm = this.fb.group({
    name: ['', Validators.required],
    type: ['', Validators.required],
  })

  createFormUtils = new FormGroupUtils(this.createForm)

  isCreating = signal(false)

  sensorTypes = computed(() => {
    return Object.values(SensorType)
  })

  onCancel() {
    setTimeout(() => {
      this.createForm.reset()
    }, 250)
  }

  onSubmitCreate() {
    this.createForm.markAllAsTouched()

    if (!this.createForm.valid) return

    this.isCreating.set(true)

    const { name, type } = this.createForm.value

    this.sensorApiService
      .create({
        name: name!,
        type: type! as SensorType,
      })
      .subscribe(response => {
        this.isCreating.set(false)
        if (!response) return

        this.sensorToken()(response.sensorToken)
        this.isSyncing()(true)

        this.authService.setSensors([
          ...this.authService.user()!.sensors,
          response.sensor,
        ])

        this.createForm.reset()
      })
  }
}
