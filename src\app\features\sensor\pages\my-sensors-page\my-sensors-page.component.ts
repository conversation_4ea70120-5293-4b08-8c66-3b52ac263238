import { JsonPipe } from '@angular/common'
import {
  Component,
  computed,
  inject,
  linkedSignal,
  OnInit,
  signal,
} from '@angular/core'
import { AuthService } from '@auth/services/auth.service'
import { SensorTypePipe } from '../../pipes/sensor-type.pipe'
import { RouterLink } from '@angular/router'
import { CreateSensorModalComponent } from '../../components/create-sensor-modal/create-sensor-modal.component'
import { MeasurementSocketService } from '../../services/ws/measurements-socket.service'
import { SensorsStatusService } from '../../services/ws/sensors-status.service'

@Component({
  selector: 'app-my-sensors-page',
  imports: [SensorTypePipe, CreateSensorModalComponent, RouterLink],
  templateUrl: './my-sensors-page.component.html',
})
export default class MySensorsPageComponent {
  authService = inject(AuthService)

  measurementService = inject(MeasurementSocketService)

  sensorsStatusService = inject(SensorsStatusService)

  query = signal('')

  sensorsStatus = this.sensorsStatusService.sensorsStatus

  connectedSensors = computed(() => {
    if (this.sensorsStatus() === null) return []
    return this.sensorsStatus()!
      .filter(s => s.connected)
      .map(s => s.sensorId)
  })

  userHasSensors = computed(() => {
    const sensors = this.authService.user()?.sensors
    return sensors !== undefined && sensors.length !== 0
  })

  sensors = computed(() => {
    const query = this.query().toLowerCase().trim()

    const sensors = this.authService.user()?.sensors!

    if (!this.query()) return sensors

    return sensors.filter(s => s.name.toLowerCase().includes(query))
  })
}
