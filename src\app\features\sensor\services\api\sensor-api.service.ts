import { HttpClient } from '@angular/common/http'
import { inject, Injectable } from '@angular/core'
import { catchError, map, Observable, of } from 'rxjs'
import { CreateSensorDTO } from './dto/create-sensor.dto'
import { CreateSensorResponse } from './api-responses/create-sensor.response'
import { environment } from '@environments/environment.development'
import { AuthService } from '@auth/services/auth.service'
import { FindOneSensorDTO } from './dto/find-one-sensor.dto'
import { FindOneSensorResponse } from './api-responses/find-one-sensor.response'
import { FindManyMeasurementsResponse } from './api-responses/find-many-measurements.response'
import { FindManyMeasurementsDTO } from './dto/find-many-measurements.dto'
import { Pagination } from '@common/pagination/pagination.interface'

@Injectable({ providedIn: 'root' })
export class SensorApiService {
  private http = inject(HttpClient)

  private authService = inject(AuthService)

  create(dto: CreateSensorDTO): Observable<CreateSensorResponse | null> {
    return this.http
      .post<CreateSensorResponse>(
        `http://${environment.baseUrl}/sensor/add-sensor-user`,
        dto,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error creando sensor: ${resp}`)
          return of(null)
        })
      )
  }

  findOne(dto: FindOneSensorDTO): Observable<FindOneSensorResponse | null> {
    return this.http
      .get<FindOneSensorResponse>(
        `http://${environment.baseUrl}/sensor/one/${dto.id}`,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe()
  }

  findManyMeasurements(
    dto: FindManyMeasurementsDTO
  ): Observable<FindManyMeasurementsResponse[] | null> {
    return this.http
      .get<FindManyMeasurementsResponse[]>(
        `http://${environment.baseUrl}/sensor/many/${dto.sensorId}`,
        {
          params: {
            startDate: dto.startDate.toISOString(),
            endDate: dto.endDate.toISOString(),
          },
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error obteniendo mediciones: ${JSON.stringify(resp)}`)
          return of(null)
        })
      )
  }
}
