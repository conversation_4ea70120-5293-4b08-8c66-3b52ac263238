import { Injectable, signal, inject } from '@angular/core'
import { MeasurementSocketService } from './measurements-socket.service'

export type SensorStatus = {
  connected: boolean
  sensorId: string
}

@Injectable({ providedIn: 'root' })
export class SensorsStatusService {
  sensorsStatus = signal<SensorStatus[] | null>(null)

  measurementService = inject(MeasurementSocketService)

  constructor() {
    this.measurementService.onMessage('sensor-connection', resp => {
      this.sensorsStatus.update(value => {
        const connectedIdSensor = resp.sensorId

        if (value === null) return null

        return value.map(s => {
          if (s.sensorId === connectedIdSensor) {
            return {
              sensorId: s.sensorId,
              connected: true,
            }
          }
          return s
        })
      })
    })
    this.measurementService.onMessage('sensor-disconnection', resp => {
      this.sensorsStatus.update(value => {
        const connectedIdSensor = resp.sensorId

        if (value === null) return null

        return value.map(s => {
          if (s.sensorId === connectedIdSensor) {
            return {
              sensorId: s.sensorId,
              connected: false,
            }
          }
          return s
        })
      })
    })
    this.measurementService.onMessage('sensors-status', resp => {
      this.sensorsStatus.set(resp)
    })
    this.measurementService.sendMessage('get-sensors-status')
  }
}
