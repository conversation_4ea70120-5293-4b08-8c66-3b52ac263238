import { Component, computed, inject, signal, WritableSignal } from '@angular/core'
import { RouterLink } from '@angular/router'
import { TriggerApiService } from '../../services/trigger-api.service'
import { Trigger } from '../../interfaces/trigger'
import { AuthService } from '@auth/services/auth.service'

@Component({
  selector: 'app-active-triggers-page',
  imports: [RouterLink],
  templateUrl: './active-triggers-page.component.html',
})
export class ActiveTriggersPageComponent {
  authService = inject(AuthService)
  query = signal('')

  userTriggers: WritableSignal<Trigger[]> = signal([])

  triggers = computed(() => {
    if (this.query() === '') {
      return this.userTriggers()
    }
    return this.userTriggers().filter(trigger =>
      trigger.name.toLowerCase().includes(this.query().toLowerCase())
    )
  })

  userHasTriggers = computed(() => {
    return this.triggers() !== undefined && this.triggers().length !== 0
  })

  constructor() {
    this.userTriggers.set(
      this.authService.user()?.triggers ? this.authService.user()!.triggers : []
    )
  }
}
