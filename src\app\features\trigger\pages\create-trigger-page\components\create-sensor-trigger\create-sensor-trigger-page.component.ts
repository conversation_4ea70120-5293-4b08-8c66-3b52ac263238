import {
  Component,
  computed,
  EventEmitter,
  inject,
  output,
  Output,
  signal,
  WritableSignal,
} from '@angular/core'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { Sensor } from '@auth/interfaces/user'
import { AuthService } from '@auth/services/auth.service'
import { MeasurementUnitPipe } from 'src/app/features/sensor/pipes/measurement-unit.pipe'
import { ComparisonTypesEnum } from 'src/app/features/trigger/interfaces/comparison-types'
import { ComparisonPipe } from 'src/app/features/trigger/pipes/comparisons.pipe'

@Component({
  selector: 'app-create-sensor-trigger-page',
  imports: [ReactiveFormsModule, FormsModule, ComparisonPipe, MeasurementUnitPipe],
  templateUrl: './create-sensor-trigger-page.component.html',
})
export class CreateSensorTriggerPageComponent {
  private authService = inject(AuthService)

  userSensors: WritableSignal<Sensor[]> = signal([])

  selectedSensor: WritableSignal<string> = signal('')

  selectedSensorType = computed(() => {
    const sensor = this.userSensors().find(s => s.id == this.selectedSensor())
    return sensor ? sensor.type : null
  });

  comparisons = computed(() => {
    return Object.values(ComparisonTypesEnum)
  })

  selectedComparison: WritableSignal<ComparisonTypesEnum> = signal(
    ComparisonTypesEnum.eq
  )

  sensorError = computed(() => {
    if (this.selectedSensor() != '') {
      if (
        (this.selectedComparison() == this.comparisons()[6] ||
          this.selectedComparison() == this.comparisons()[7]) &&
        this.initialMeasure() >= this.endMeasure()
      ) {
        this.emitVoidData()
        return 'Seleccione un rango válido'
      } else {
        this.emitData()
        return ''
      }
    } else {
      this.emitVoidData()
      return 'Seleccione un sensor'
    }
  })

  initialMeasure = signal(0)

  endMeasure = signal(0)

  dataEvent = output<{}>()
  constructor() {
    this.userSensors.set(
      this.authService.user()?.sensors ? this.authService.user()!.sensors : []
    )
  }

  emitData() {
    this.dataEvent.emit({
      sensorId: this.selectedSensor(),
      objectiveMeasures:
        this.selectedComparison() == this.comparisons()[6] ||
        this.selectedComparison() == this.comparisons()[7]
          ? [this.initialMeasure(), this.endMeasure()]
          : [this.initialMeasure()],
      comparison: this.selectedComparison(),
    })
  }

  emitVoidData() {
    this.dataEvent.emit('')
  }
}
