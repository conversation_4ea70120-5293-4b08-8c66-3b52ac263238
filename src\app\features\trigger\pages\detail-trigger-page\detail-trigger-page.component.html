<div class="w-full flex justify-start p-4 items-center">
  <div
    class="flex items-center justify-center w-10 h-10 rounded-full bg-blue-500 text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 cursor-pointer"
    routerLink="/trigger">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-6 w-6"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor">
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M10 19l-7-7m0 0l7-7m-7 7h18" />
    </svg>
  </div>
  <div class="w-4/5 justify-center flex">
    <h1 class="text-2xl">Ver Trigger</h1>
  </div>
</div>

<div class="w-full flex justify-center gap-2">
  @if (triggerType() == 'time') {
    <div class="w-2/5">
      <input
        id="time-option"
        type="checkbox"
        value="time"
        [checked]="triggerType() == 'time'"
        class="hidden peer" />
      <label
        for="time-option"
        class="flex w-full p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700 justify-center peer-checked:border-blue-600 dark:peer-checked:border-blue-600 hover:text-gray-600 dark:peer-checked:text-gray-300 peer-checked:text-gray-600 hover:bg-gray-50 dark:text-gray-400 dark:hover:bg-gray-700">
        <div class="w-full text-sm flex align-middle justify-center">
          Tiempo
        </div>
      </label>
    </div>
  } @else {
    <div class="w-2/5">
      <input
        id="sensor-option"
        type="checkbox"
        value="sensor"
        [checked]="triggerType() == 'sensor'"
        class="hidden peer" />
      <label
        for="sensor-option"
        class="flex w-full p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700 justify-center peer-checked:border-blue-600 dark:peer-checked:border-blue-600 hover:text-gray-600 dark:peer-checked:text-gray-300 peer-checked:text-gray-600 hover:bg-gray-50 dark:text-gray-400 dark:hover:bg-gray-700">
        <div class="w-full text-sm flex align-middle justify-center">
          Sensor
        </div>
      </label>
    </div>
  }
</div>

<form autocomplete="off" class="w-full p-5">
  <label class="label pb-3 text-xl">Nombre</label>
  <div class="">
    <input
      type="text"
      id="appt-time"
      name="appt-time"
      class="input"
      [value]="trigger()?.name"
      readonly />
  </div>

  @if (triggerType() == 'time') {
    <app-detail-time-trigger-page></app-detail-time-trigger-page>
  } @else {
    <app-detail-sensor-trigger-page></app-detail-sensor-trigger-page>
  }
    <div class="flex w-full justify-between mt-5">
      <div class="w-2/5 text-xl font-bold">Actuador</div>
      <div class="w-2/5 text-xl font-bold">Acción</div>
      <div class="w-1/5"></div>
    </div>
    @for (actuatorA of this.actuatorActions(); track $index) {
      <div class="flex w-full justify-between mt-5 mb-5">
        <div class="w-2/5 text-lg">{{ actuatorA?.actuatorName }}</div>
        <div class="w-2/5 text-lg">{{ actuatorA?.actions! | actionPipe }}</div>
        <div class="w-1/5 flex items-center"></div>
      </div>
    }
</form>
