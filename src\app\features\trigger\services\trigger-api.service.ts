import { HttpClient } from '@angular/common/http'
import { inject, Injectable } from '@angular/core'
import { catchError, Observable, of } from 'rxjs'
import { environment } from '@environments/environment.development'
import { AuthService } from '@auth/services/auth.service'
import { CreateTriggerDTO } from './dto/create-trigger.dto'
import { CreateTriggerResponse } from './api-responses/create-trigger.response'
import { PaginationDTO } from '@common/dto/pagination.dto'
import { Pagination } from '@common/pagination/pagination.interface'
import { Trigger } from '../interfaces/trigger'

@Injectable({ providedIn: 'root' })
export class TriggerApiService {
  private http = inject(HttpClient)

  private authService = inject(AuthService)

  create(dto: CreateTriggerDTO): Observable<CreateTriggerResponse | null> {
    return this.http
      .post<CreateTriggerResponse>(
        `http://${environment.baseUrl}/trigger/create-trigger`,
        dto,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error creando Trigger: ${resp}`)
          return of(null)
        })
      )
  }

  getAll(dto?: PaginationDTO): Observable<Pagination<Trigger> | null> {
    const pagination = dto?.limit ? `?limit=${dto.limit}&page=${dto.page}` : ''
    return this.http
      .get<Pagination<Trigger>>(
        `http://${environment.baseUrl}/trigger/many` + pagination,
        {
          headers: {
            Authorization: `Bearer ${this.authService.token()}`,
          },
        }
      )
      .pipe(
        catchError(resp => {
          console.error(`Error obteniendo triggers: ${resp}`)
          return of(null)
        })
      )
  }
}
