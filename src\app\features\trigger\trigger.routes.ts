import { Routes } from '@angular/router'

export const triggerRoutes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import(
        './pages/active-triggers-page/active-triggers-page.component'
      ).then(m => m.ActiveTriggersPageComponent),
  },
  {
    path: 'create',
    loadComponent: () =>
      import(
        './pages/create-trigger-page/create-trigger-page.component'
      ).then(m => m.CreateTriggerPageComponent),
  },
  {
    path: 'detail/:triggerId',
    loadComponent: () =>
      import(
        './pages/detail-trigger-page/detail-trigger-page.component'
      ).then(m => m.DetailTriggerPageComponent),
  }

]

export default triggerRoutes
